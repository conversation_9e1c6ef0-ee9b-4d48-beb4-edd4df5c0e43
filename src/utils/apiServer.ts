'use server'
import ky from 'ky'
import { destinationUrl } from './constants'
import { cookies } from 'next/headers'

// 检测是否在构建阶段
const isBuilding =
  process.env.NEXT_PHASE === 'phase-production-build' ||
  process.env.NEXT_PHASE === 'phase-export'

console.log('isBuilding 11111', isBuilding)

const createServerApi = (customPrefixUrl?: string) => {
  return ky.create({
    prefixUrl: customPrefixUrl || `${destinationUrl}/common-api/v1`,
    credentials: 'include',
    hooks: {
      beforeRequest: [
        async (request) => {
          if (!isBuilding) {
            const cookieStore = await cookies()
            const cookieString = cookieStore
              .getAll()
              .map((cookie) => `${cookie.name}=${cookie.value}`)
              .join('; ')
            request.headers.set('cookie', cookieString)

            // 添加调试日志，帮助排查问题
            console.log('apiServer beforeRequest:', {
              url: request.url,
              cookieCount: cookieStore.getAll().length,
              hasCookie: cookieString.length > 0,
              cookies: cookieString.substring(0, 200) + (cookieString.length > 200 ? '...' : '')
            })
          }
        }
      ],
      afterResponse: [
        async (request, options, response) => {
          // 添加响应日志，帮助排查401错误
          if (response.status === 401) {
            console.error('apiServer 401 Error:', {
              url: request.url,
              status: response.status,
              statusText: response.statusText
            })
          }
          return response
        }
      ]
    }
  })
}

export const apiServer = createServerApi()

export default apiServer
