/**
 * 认证配置统一管理
 * 解决环境变量不一致的问题
 */

/**
 * 获取统一的 Token Key
 * 确保 Server Action 和其他地方使用相同的 token key
 */
export function getTokenKey(): string {
  // 生产环境使用 MINUTES_ACCESS_TOKEN-dev
  // 开发环境可以使用不同的 key
  const tokenKey = process.env.TOKEN_KEY || 'MINUTES_ACCESS_TOKEN-dev'
  
  // 添加调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log('getTokenKey:', tokenKey)
  }
  
  return tokenKey
}

/**
 * 获取统一的应用名称
 * 确保与 PHP 后端保持一致
 */
export function getAppName(): string {
  // 使用 PROJECT_NAME 环境变量，与 PHP 后端的 APP_NAME 保持一致
  const appName = process.env.PROJECT_NAME || 'Minutes'
  
  if (process.env.NODE_ENV === 'development') {
    console.log('getAppName:', appName)
  }
  
  return appName
}

/**
 * 获取 Redis 配置
 * 确保使用正确的数据库
 */
export function getRedisConfig() {
  return {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : 6379,
    password: process.env.REDIS_PASSWORD,
    // PHP 后端使用 'frontend' 池，对应数据库1
    db: 1
  }
}

/**
 * 生成 Redis Token Key
 * 与 PHP 后端保持一致的格式
 */
export function getRedisTokenKey(uid: string | number): string {
  const appName = getAppName()
  return `${appName}:u:token:${uid}`
}

/**
 * 验证配置是否正确
 */
export function validateAuthConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!process.env.TOKEN_KEY) {
    errors.push('TOKEN_KEY environment variable is not set')
  }
  
  if (!process.env.PROJECT_NAME) {
    errors.push('PROJECT_NAME environment variable is not set')
  }
  
  if (!process.env.REDIS_HOST) {
    errors.push('REDIS_HOST environment variable is not set')
  }
  
  if (!process.env.NEXT_PUBLIC_BASE_URL) {
    errors.push('NEXT_PUBLIC_BASE_URL environment variable is not set')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 打印配置信息（仅在开发环境）
 */
export function debugAuthConfig() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Auth Config Debug:', {
      tokenKey: getTokenKey(),
      appName: getAppName(),
      redisConfig: getRedisConfig(),
      validation: validateAuthConfig()
    })
  }
}
