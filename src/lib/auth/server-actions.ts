'use server'

/**
 * 认证相关的 Server Actions
 * 这个文件专门用于需要 'use server' 指令的异步函数
 */

import { cookies } from 'next/headers'
import { verifyJwtToken } from '@/services/actions/authServer'
import { getTokenKey as getConfigTokenKey, debugAuthConfig } from './config'

// 认证结果类型
interface AuthResult {
  success: boolean
  userId?: number
  error?: string
}

/**
 * 核心认证函数（基于现有代码）
 * 这是一个 Server Action，可以在客户端组件中调用
 */
export async function authenticateUser(): Promise<AuthResult> {
  try {
    // 在开发环境打印配置信息
    if (process.env.NODE_ENV === 'development') {
      debugAuthConfig()
    }

    // 从 cookie 中获取 token（使用统一配置）
    const cookieStore = await cookies()
    const tokenKey = getConfigTokenKey()
    const token = cookieStore.get(tokenKey)?.value

    console.log('authenticateUser:', {
      tokenKey,
      hasToken: !!token,
      tokenLength: token?.length || 0
    })

    if (!token) {
      return {
        success: false,
        error: 'No token found'
      }
    }

    // 验证 token（使用现有的 verifyJwtToken 函数）
    const { isValid, uid } = await verifyJwtToken(token)

    if (!isValid || !uid) {
      return {
        success: false,
        error: 'Invalid token'
      }
    }

    return {
      success: true,
      userId: Number(uid)
    }
  } catch (error) {
    console.error('Authentication failed:', error)
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

/**
 * 检查是否已登录的 Server Action
 * 可以在客户端组件中调用
 */
export async function checkAuthStatus(): Promise<boolean> {
  const authResult = await authenticateUser()
  return authResult.success
}

/**
 * 获取当前用户信息的 Server Action
 * 可以在客户端组件中调用
 */
export async function getCurrentUserInfo(): Promise<{ userId: number } | null> {
  const authResult = await authenticateUser()
  return authResult.success ? { userId: authResult.userId! } : null
}

/**
 * 获取完整用户信息的 Server Action
 * 可以在客户端组件中调用，返回包含 rank_name、vip_expired_at 等完整字段的用户数据
 */
export async function getFullUserInfo(): Promise<User | null> {
  try {
    // 首先验证认证状态
    const authResult = await authenticateUser()
    if (!authResult.success) {
      console.log('getFullUserInfo: Authentication failed')
      return null
    }

    // 动态导入 getUserInfo 以避免客户端导入 server-only 模块
    const { getUserInfo } = await import('@/services/server/userService')

    // 获取完整用户信息
    const userInfo = await getUserInfo()
    console.log('getFullUserInfo: Retrieved user info:', userInfo)

    return userInfo
  } catch (error) {
    console.error('getFullUserInfo: Failed to get user info:', error)
    return null
  }
}
