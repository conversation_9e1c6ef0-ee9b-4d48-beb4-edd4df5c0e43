'server-only'
import { Redis } from 'ioredis'

let redis: Redis | null = null

export function getRedisClient(db: number = 1) {
  console.log('getRedisClient', process.env.REDIS_HOST, process.env.REDIS_PORT, process.env.REDIS_PASSWORD)
  if (!redis) {
    redis = new Redis({
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : undefined,
      password: process.env.REDIS_PASSWORD,
      db
    })
  }

  return redis
}
