import { Link } from 'next-view-transitions'
import { getUserInfo } from '@/services/server/userService';
import { getCategoriesForBrowse } from '@/services/category.service';
import { convertCategoriesToDropdownFormat } from '@/utils/category.utils';
import { getLocale } from 'next-intl/server';
import { Avatar } from './Avatar';
import { LoginButtons } from './LoginButtons';
import { MobileMenuClient } from './MobileMenuClient';
import BrowseDropdown from './BrowseDropdown';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { LibraryLoginLink } from './LibraryLoginLink';

// 动态导入客户端组件，避免服务端渲染错误
const SearchForm = dynamic(() => import('./SearchForm'));

export default async function Header() {
  const userInfo = await getUserInfo();

  // 获取当前语言
  const locale = await getLocale();

  // 动态获取分类数据（专门为Browse下拉菜单优化）
  let categories: { name: string; href: string }[] = [];
  try {
    const categoriesData = await getCategoriesForBrowse(locale);
    categories = convertCategoriesToDropdownFormat(categoriesData);
  } catch (error) {
    console.error('Failed to fetch categories for header:', error);
    // 如果获取失败，categories 保持为空数组，BrowseDropdown 将显示空菜单
  }

  return (
    <header className="sticky top-0 z-50 w-full">
      <nav className="bg-white bg-opacity-40 backdrop-blur-lg shadow-sm transition-all duration-300 border-gray-200/50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-3">
            <div className="flex items-center space-x-6">
              <Link className="text-gray-600 hover:text-green-500" href="/">
                <Image src='/logo.svg' alt="15minutes" width={140} height={36} />
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link className="text-gray-600 hover:text-green-500" href="/">Home</Link>
                {
                  userInfo ? <Link className="text-gray-600 hover:text-green-500" href="/my-library">My Library</Link> : <LibraryLoginLink />
                }

                {/* 使用客户端组件处理下拉菜单 */}
                <BrowseDropdown categories={categories} />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:block relative">
                <SearchForm />
              </div>
              <Link className="hidden md:block text-gray-600 hover:text-green-500" href="/pricing">Pricing</Link>

              {userInfo ? <Avatar /> : <LoginButtons />}

              <MobileMenuClient categories={categories} />
            </div>

          </div>
        </div>
      </nav>
    </header>
  );
}
