import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { verifyJwtToken } from '@/services/actions/authServer'
import { getRedisClient } from '@/lib/redis'

/**
 * 调试认证状态的 API 端点
 * GET /api/debug/auth
 * 
 * 这个端点用于调试认证问题，显示：
 * 1. Cookie 信息
 * 2. Token 验证状态
 * 3. Redis 中的 token 存储情况
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    
    // 获取所有可能的 token key
    const tokenKeys = [
      process.env.TOKEN_KEY || 'MINUTES_ACCESS_TOKEN-dev',
      'TAROT_ACCESS_TOKEN-dev',
      'TEST_ACCESS_TOKEN-dev'
    ]
    
    const tokenInfo: Record<string, any> = {}
    
    for (const tokenKey of tokenKeys) {
      const token = cookieStore.get(tokenKey)?.value
      tokenInfo[tokenKey] = {
        exists: !!token,
        value: token ? `${token.substring(0, 20)}...` : null
      }
      
      if (token) {
        try {
          // 验证 token
          const verifyResult = await verifyJwtToken(token)
          tokenInfo[tokenKey].verification = verifyResult
          
          if (verifyResult.uid) {
            // 检查 Redis 中的存储情况
            const redis1 = getRedisClient(1) // 数据库1
            const redis0 = getRedisClient(0) // 数据库0
            
            const appName = process.env.PROJECT_NAME || 'Minutes'
            const redisKey = `${appName}:u:token:${verifyResult.uid}`
            
            const existsInDb1 = await redis1.exists(redisKey)
            const existsInDb0 = await redis0.exists(redisKey)
            
            tokenInfo[tokenKey].redis = {
              key: redisKey,
              existsInDb1,
              existsInDb0,
              appName
            }
          }
        } catch (error) {
          tokenInfo[tokenKey].error = error instanceof Error ? error.message : String(error)
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      debug: {
        environment: {
          NODE_ENV: process.env.NODE_ENV,
          PROJECT_NAME: process.env.PROJECT_NAME,
          TOKEN_KEY: process.env.TOKEN_KEY,
          NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL
        },
        cookies: {
          total: allCookies.length,
          list: allCookies.map(cookie => ({
            name: cookie.name,
            hasValue: !!cookie.value,
            valueLength: cookie.value?.length || 0
          }))
        },
        tokens: tokenInfo,
        redis: {
          host: process.env.REDIS_HOST,
          port: process.env.REDIS_PORT,
          db: process.env.REDIS_DB
        }
      }
    })
  } catch (error) {
    console.error('Debug auth error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
