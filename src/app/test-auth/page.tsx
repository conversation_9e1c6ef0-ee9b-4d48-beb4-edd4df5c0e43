import { getUserInfo } from '@/services/server/userService'
import { cookies } from 'next/headers'

export default async function TestAuthPage() {
  // 获取所有Cookie
  const cookieStore = await cookies()
  const allCookies = cookieStore.getAll()
  
  // 获取用户信息
  const userInfo = await getUserInfo()
  
  // 获取特定的认证Cookie
  const authToken = cookieStore.get('MINUTES_ACCESS_TOKEN')
  
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">认证状态测试页面</h1>
      
      <div className="space-y-6">
        {/* Cookie信息 */}
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Cookie信息</h2>
          <p><strong>总Cookie数量:</strong> {allCookies.length}</p>
          <p><strong>认证Token存在:</strong> {authToken ? '是' : '否'}</p>
          {authToken && (
            <p><strong>Token长度:</strong> {authToken.value.length}</p>
          )}
          
          <details className="mt-2">
            <summary className="cursor-pointer">查看所有Cookie</summary>
            <pre className="mt-2 text-sm bg-white p-2 rounded overflow-auto">
              {JSON.stringify(allCookies.map(c => ({ name: c.name, hasValue: !!c.value, length: c.value.length })), null, 2)}
            </pre>
          </details>
        </div>
        
        {/* 用户信息 */}
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">用户信息</h2>
          {userInfo ? (
            <div>
              <p><strong>用户邮箱:</strong> {userInfo.email}</p>
              <p><strong>用户名:</strong> {userInfo.username}</p>
              <p><strong>等级:</strong> {userInfo.rank_name}</p>
              <p><strong>头像:</strong> {userInfo.avatar ? '有' : '无'}</p>
              
              <details className="mt-2">
                <summary className="cursor-pointer">查看完整用户信息</summary>
                <pre className="mt-2 text-sm bg-white p-2 rounded overflow-auto">
                  {JSON.stringify(userInfo, null, 2)}
                </pre>
              </details>
            </div>
          ) : (
            <p className="text-red-600">❌ 未获取到用户信息</p>
          )}
        </div>
        
        {/* 测试结果 */}
        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">测试结果</h2>
          <div className="space-y-1">
            <p>✅ Cookie传递: {allCookies.length > 0 ? '正常' : '异常'}</p>
            <p>✅ 认证Token: {authToken ? '存在' : '缺失'}</p>
            <p>✅ 用户信息: {userInfo ? '获取成功' : '获取失败'}</p>
            <p>✅ 服务端认证: {userInfo ? '正常' : '异常'}</p>
          </div>
        </div>
        
        {/* 操作建议 */}
        <div className="bg-yellow-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">操作建议</h2>
          {userInfo ? (
            <div>
              <p className="text-green-600">🎉 认证状态正常！如果头部仍未显示用户信息，请：</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>强制刷新页面 (Cmd+Shift+R)</li>
                <li>清除浏览器缓存</li>
                <li>检查头部组件的渲染逻辑</li>
              </ul>
            </div>
          ) : (
            <div>
              <p className="text-red-600">❌ 认证状态异常，请：</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>检查是否已正确登录</li>
                <li>查看浏览器控制台错误</li>
                <li>访问调试接口确认token状态</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
