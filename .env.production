CLIENT_ID=1063288663323-hn4f5s5hbsdermij3v5430m1fr6p9p7c.apps.googleusercontent.com

NEXT_PUBLIC_BASE_URL = "https://15minutes.ai"
DATABASE_URL=mysql://15minutes:mZMXpny8tymjpYKY@**********:3306/15minutes
MONGODB_URL="***************************************************************************************************************"

PROJECT_NAME="Minutes"
REDIS_HOST="***********"
REDIS_PORT="6379"
REDIS_PASSWORD="REDIS!123SDFdfsfe"
REDIS_DB=1
RABBITMQ_URL="*********************************************"
JWT_SECRET=""
TOKEN_KEY="MINUTES_ACCESS_TOKEN-dev"

DB_HOST=**********
DB_USER=15minutes
DB_PASSWORD=mZMXpny8tymjpYKY
DB_NAME=15minutes

# Cloudflare R2 配置（S3兼容）
AWS_ACCESS_KEY_ID=********************************
AWS_SECRET_ACCESS_KEY=0cd209fedf762e5bfe11570f4d0c4ee43361562e156b6ad8377bd8b714d693ac
AWS_S3_REGION=auto
AWS_S3_BUCKET=15minutes

# 关键：使用Cloudflare R2的自定义endpoint
S3_ENDPOINT=https://5a0eb824d75b14ed9749622edffadc9f.r2.cloudflarestorage.com
S3_CDN_URL=https://cdn.15minutes.ai

# 音频文件存储路径前缀
AWS_S3_AUDIO_PREFIX=audio/

# ElevenLabs配置
ELEVENLABS_API_KEY=***************************************************
ELEVENLABS_MODEL_ID=eleven_flash_v2_5
ELEVENLABS_OUTPUT_FORMAT=mp3_44100_128

# 其他音频配置
AUDIO_CACHE_TTL=259200
AUDIO_DEFAULT_VOICE_EN=21m00Tcm4TlvDq8ikWAM
AUDIO_DEFAULT_VOICE_ZH=pNInz6obpgDQGcFmaJgB
AUDIO_MAX_FILE_SIZE=47262976
AUDIO_GENERATION_TIMEOUT=30000
AUDIO_LOCK_TTL=60000
AUDIO_MAX_WAIT_TIME=30000